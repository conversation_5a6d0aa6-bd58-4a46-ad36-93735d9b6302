"use client";

import type { PropsWithChildren } from "react";

import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { Button } from "~/components/ui/button";
import { useIsMobile } from "~/hooks/use-mobile";
import { routes } from "~/lib/routes";
import { cn } from "~/lib/utils";
import { ConversationsList } from "./_components/conversations-list";

export default function ConversationsLayout({ children }: PropsWithChildren) {
  const params = useParams();
  const isMobile = useIsMobile();

  const hasSelectedConversation = !!params.id;

  if (isMobile) {
    return (
      <div className={cn("h-full flex flex-col")}>
        {hasSelectedConversation ? (
          <div className={cn("flex flex-col h-full")}>
            {/* Mobile back button */}
            <div
              className={cn(
                "flex items-center p-3 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
              )}
            >
              <Link href={routes.app.user.conversations.url()}>
                <Button variant="ghost" size="sm" className={cn("mr-2")}>
                  <ArrowLeft className={cn("size-4")} />
                  <span className={cn("ml-1")}>Back</span>
                </Button>
              </Link>
            </div>
            <div className={cn("flex-1 min-h-0")}>{children}</div>
          </div>
        ) : (
          <ConversationsList />
        )}
      </div>
    );
  }

  return (
    <div className={cn("grid grid-cols-1 lg:grid-cols-4 gap-0 h-full")}>
      <div
        className={cn(
          "col-span-1 h-full overflow-hidden border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
        )}
      >
        <ConversationsList />
      </div>
      <div
        className={cn(
          "col-span-1 lg:col-span-3 h-full overflow-hidden bg-gray-50 dark:bg-gray-900"
        )}
      >
        {hasSelectedConversation ? (
          children
        ) : (
          <div
            className={cn(
              "flex items-center justify-center h-full text-gray-500 dark:text-gray-400"
            )}
          >
            <div className={cn("text-center")}>
              <p className={cn("text-lg font-medium mb-2")}>
                Select a conversation
              </p>
              <p className={cn("text-sm")}>
                Choose a conversation from the list to start chatting
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
