"use client";

import { ChevronDown } from "lucide-react";

import type { PublicConversation, PublicMessage } from "~/lib/types";
import { Button } from "~/components/ui/button";
import { ConversationEndedBy } from "~/lib/types";
import { useChatScroll } from "~/hooks/use-chat-scroll";
import { cn } from "~/lib/utils";
import { ConversationHeader } from "./conversation-header";
import { ConversationInput } from "./conversation-input";
import { MessageBubble } from "./message-bubble";

interface ConversationProps {
  conversation: PublicConversation;
  messages: PublicMessage[];
  currentUserId: string;
}

export function Conversation({
  conversation,
  messages,
  currentUserId,
}: ConversationProps) {
  const isEnded = !!conversation.endedBy;

  const {
    messagesContainerRef,
    messagesEndRef,
    handleScroll,
    scrollToBottom,
    shouldAutoScroll,
  } = useChatScroll({
    messages,
  });

  const getDisabledReason = () => {
    if (conversation.endedBy === ConversationEndedBy.SYSTEM) {
      return "This conversation has been ended by the system";
    }
    if (conversation.endedBy === ConversationEndedBy.ADMIN) {
      return "This conversation has been ended by an administrator";
    }
    return "This conversation has been ended";
  };

  return (
    <div className={cn("flex flex-col h-full")}>
      <ConversationHeader conversation={conversation} />
      <div className={cn("relative flex-1 overflow-hidden")}>
        <div
          ref={messagesContainerRef}
          onScroll={handleScroll}
          className={cn(
            "h-full overflow-y-auto overflow-x-hidden",
            "px-3 sm:px-4 py-4 sm:py-6",
            "bg-gray-50 dark:bg-gray-900 scroll-smooth",
            "scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent",
            isEnded && "bg-red-50/50 dark:bg-red-950/10"
          )}
        >
          <div
            className={cn(
              "space-y-3 sm:space-y-4 min-h-full flex flex-col justify-end"
            )}
          >
            {messages.map((message, index) => (
              <MessageBubble
                key={message.id}
                message={message}
                isOwnMessage={message.sender.id === currentUserId}
                showAvatar={
                  index === 0 ||
                  messages[index - 1]?.sender.id !== message.sender.id
                }
              />
            ))}
            <div ref={messagesEndRef} className={cn("h-1")} />
          </div>
        </div>

        {/* Scroll to bottom button */}
        {!shouldAutoScroll && (
          <Button
            onClick={() => scrollToBottom(true)}
            size="sm"
            variant="secondary"
            className={cn(
              "absolute bottom-4 right-4 rounded-full shadow-lg",
              "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
              "hover:bg-gray-50 dark:hover:bg-gray-700",
              "transition-all duration-200 ease-in-out",
              "z-10"
            )}
          >
            <ChevronDown className={cn("size-4")} />
          </Button>
        )}
      </div>
      <ConversationInput
        onSendMessage={() => {}}
        isDisabled={isEnded}
        disabledReason={getDisabledReason()}
      />
    </div>
  );
}
