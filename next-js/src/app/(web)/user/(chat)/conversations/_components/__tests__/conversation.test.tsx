import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Conversation } from '../conversation';
import { ConversationEndedBy, ConversationType } from '~/lib/types';

// Mock the custom hook
jest.mock('~/hooks/use-chat-scroll', () => ({
  useChatScroll: () => ({
    messagesContainerRef: { current: null },
    messagesEndRef: { current: null },
    handleScroll: jest.fn(),
    scrollToBottom: jest.fn(),
    shouldAutoScroll: true,
  }),
}));

const mockConversation = {
  id: '1',
  referenceId: '1',
  type: ConversationType.VENDOR,
  members: [
    { id: '1', name: '<PERSON>', pictureId: '1' },
    { id: '2', name: '<PERSON>', pictureId: '2' },
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockMessages = [
  {
    id: 'msg1',
    content: 'Hello there!',
    sender: mockConversation.members[0],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    id: 'msg2',
    content: 'Hi! How are you?',
    sender: mockConversation.members[1],
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

describe('Conversation Component', () => {
  it('renders conversation header and messages', () => {
    render(
      <Conversation
        conversation={mockConversation}
        messages={mockMessages}
        currentUserId="1"
      />
    );

    // Check if messages are rendered
    expect(screen.getByText('Hello there!')).toBeInTheDocument();
    expect(screen.getByText('Hi! How are you?')).toBeInTheDocument();
  });

  it('renders input field when conversation is not ended', () => {
    render(
      <Conversation
        conversation={mockConversation}
        messages={mockMessages}
        currentUserId="1"
      />
    );

    expect(screen.getByPlaceholderText('Type a message...')).toBeInTheDocument();
  });

  it('shows disabled state when conversation is ended', () => {
    const endedConversation = {
      ...mockConversation,
      endedBy: ConversationEndedBy.SYSTEM,
    };

    render(
      <Conversation
        conversation={endedConversation}
        messages={mockMessages}
        currentUserId="1"
      />
    );

    expect(screen.getByText(/ended by the system/i)).toBeInTheDocument();
  });

  it('applies correct styling for own messages vs other messages', () => {
    render(
      <Conversation
        conversation={mockConversation}
        messages={mockMessages}
        currentUserId="1"
      />
    );

    const messageContainers = screen.getAllByText(/Hello there!|Hi! How are you?/);
    expect(messageContainers).toHaveLength(2);
  });
});
