"use client";

import type { PublicMessage } from "~/lib/types";

import { Check, CheckCheck } from "lucide-react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import { cn } from "~/lib/utils";

interface MessageBubbleProps {
  message: PublicMessage;
  isOwnMessage: boolean;
  showAvatar?: boolean;
}

export function MessageBubble({
  message,
  isOwnMessage,
  showAvatar = true,
}: MessageBubbleProps) {
  const sender = message.sender;

  const isSeen = isOwnMessage && Math.random() > 0.5;

  return (
    <div
      className={cn(
        "flex items-end gap-2 sm:gap-3",
        isOwnMessage ? "justify-end" : "justify-start"
      )}
    >
      {!isOwnMessage && showAvatar && (
        <Avatar className={cn("size-6 sm:size-8 self-start flex-shrink-0")}>
          <AvatarImage
            src={`https://avatar.vercel.sh/${sender.pictureId}.png`}
            alt={sender.name}
          />
          <AvatarFallback>{sender.name.charAt(0)}</AvatarFallback>
        </Avatar>
      )}
      {!isOwnMessage && !showAvatar && (
        <div className={cn("size-6 sm:size-8 flex-shrink-0")} />
      )}
      <div
        className={cn(
          "max-w-[75%] sm:max-w-[70%] md:max-w-[60%] lg:max-w-[50%] xl:max-w-[45%]",
          "p-2.5 sm:p-3 rounded-lg shadow-sm",
          "bg-white dark:bg-gray-700 text-gray-800 dark:text-gray-200",
          "break-words hyphens-auto",
          isOwnMessage ? "rounded-br-none" : "rounded-bl-none"
        )}
      >
        <p className={cn("text-sm whitespace-pre-wrap")}>{message.content}</p>
        <div
          className={cn(
            "text-xs mt-1 flex items-center",
            "text-gray-400 dark:text-gray-500",
            isOwnMessage ? "justify-end" : "justify-start"
          )}
        >
          {new Date(message.createdAt).toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
          {isOwnMessage && (
            <span className={cn("ml-1")}>
              {isSeen ? (
                <CheckCheck className={cn("size-4")} />
              ) : (
                <Check className={cn("size-4")} />
              )}
            </span>
          )}
        </div>
      </div>
      {isOwnMessage && showAvatar && (
        <Avatar className={cn("size-6 sm:size-8 self-start flex-shrink-0")}>
          <AvatarImage
            src={`https://avatar.vercel.sh/${sender.pictureId}.png`}
            alt={sender.name}
          />
          <AvatarFallback>{sender.name.charAt(0)}</AvatarFallback>
        </Avatar>
      )}
      {isOwnMessage && !showAvatar && (
        <div className={cn("size-6 sm:size-8 flex-shrink-0")} />
      )}
    </div>
  );
}
