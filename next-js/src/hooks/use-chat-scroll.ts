import { useCallback, useEffect, useRef, useState } from "react";

interface UseChatScrollOptions {
  messages: unknown[];
  threshold?: number;
  scrollDelay?: number;
  scrollEndDelay?: number;
}

export function useChatScroll({
  messages,
  threshold = 100,
  scrollDelay = 50,
  scrollEndDelay = 150,
}: UseChatScrollOptions) {
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const scrollTimeoutRef = useRef<NodeJS.Timeout>();
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousMessagesLength = useRef(messages.length);

  const scrollToBottom = useCallback((smooth = false) => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: smooth ? "smooth" : "auto",
      });
    }
  }, []);

  const checkIfUserIsAtBottom = useCallback(() => {
    if (!messagesContainerRef.current) return true;

    const { scrollTop, scrollHeight, clientHeight } =
      messagesContainerRef.current;
    return scrollHeight - scrollTop - clientHeight < threshold;
  }, [threshold]);

  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const isAtBottom = checkIfUserIsAtBottom();
    setShouldAutoScroll(isAtBottom);

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set user scrolling state
    setIsUserScrolling(true);

    // Reset user scrolling state after scroll ends
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, scrollEndDelay);
  }, [checkIfUserIsAtBottom, scrollEndDelay]);

  // Auto-scroll when new messages arrive (only if user is at bottom)
  useEffect(() => {
    const hasNewMessages = messages.length > previousMessagesLength.current;
    previousMessagesLength.current = messages.length;

    if (hasNewMessages && shouldAutoScroll && !isUserScrolling) {
      // Small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom(true);
      }, scrollDelay);
    }
  }, [
    messages,
    shouldAutoScroll,
    isUserScrolling,
    scrollToBottom,
    scrollDelay,
  ]);

  // Initial scroll to bottom on mount
  useEffect(() => {
    scrollToBottom(false);
  }, [scrollToBottom]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    messagesContainerRef,
    messagesEndRef,
    handleScroll,
    scrollToBottom,
    isUserScrolling,
    shouldAutoScroll,
  };
}
